# 🔍 Lost & Found Service Testing Guide

## Overview

The Lost & Found Service is an AI-powered system that uses Google Vision API for face recognition and surveillance search capabilities. This guide provides comprehensive instructions for testing all aspects of the service.

## Prerequisites

### 1. Environment Setup
```bash
# Required environment variables in .env.local
REACT_APP_GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key
REACT_APP_GOOGLE_CLOUD_PROJECT_ID=your_project_id
```

### 2. Google Cloud Setup
- Enable Vision API in Google Cloud Console
- Create API credentials
- Set up billing (Vision API requires billing enabled)

## Testing Methods

### Method 1: Interactive Demo (Recommended)

1. **Access the Demo**
   - Navigate to Settings page in the Drishti Dashboard
   - Click "🔽 Show Demo" in the Lost & Found Service Testing section
   - The interactive demo will load with full testing capabilities

2. **Demo Features**
   - Report missing person form
   - Automated test suite
   - Real-time case management
   - Simulation tools
   - Test results logging

### Method 2: Unit Tests

```bash
# Run the comprehensive test suite
npm test -- --testPathPattern=LostAndFoundService.test.js

# Run with coverage
npm test -- --testPathPattern=LostAndFoundService.test.js --coverage
```

### Method 3: Manual API Testing

```javascript
// Initialize service
import LostAndFoundService from './services/lostAndFoundService';

const service = new LostAndFoundService();
await service.initialize({
  onPersonFound: (data) => console.log('Person found:', data),
  onNewCase: (caseData) => console.log('New case:', caseData),
  onError: (message, error) => console.error('Error:', message, error)
});
```

## Test Scenarios

### 1. Basic Functionality Tests

#### Test Case 1: Service Initialization
```javascript
// Expected: Service initializes successfully
const result = await service.initialize();
expect(result).toBe(true);
```

#### Test Case 2: Missing Person Reporting
```javascript
const personData = {
  name: 'John Doe',
  age: 30,
  description: 'Tall, brown hair',
  lastSeenLocation: 'Central Park',
  contactInfo: '<EMAIL>'
};

const caseData = await service.reportMissingPerson(personData);
// Expected: Case created with unique ID and 'active' status
```

### 2. Priority Calculation Tests

#### Test Case 3: Child Priority (High)
```javascript
const childData = { name: 'Child', age: 8 };
const case = await service.reportMissingPerson(childData);
// Expected: priority = 'high'
```

#### Test Case 4: Elderly Priority (High)
```javascript
const elderlyData = { name: 'Elder', age: 75 };
const case = await service.reportMissingPerson(elderlyData);
// Expected: priority = 'high'
```

#### Test Case 5: Medical Conditions (Critical)
```javascript
const medicalData = { 
  name: 'Patient', 
  age: 40, 
  medicalConditions: true 
};
const case = await service.reportMissingPerson(medicalData);
// Expected: priority = 'critical'
```

### 3. Face Recognition Tests

#### Test Case 6: Face Feature Extraction
```javascript
const imageData = 'base64-encoded-image';
const features = await service.extractFaceFeatures(imageData);
// Expected: Returns facial landmarks and detection confidence
```

#### Test Case 7: Face Comparison
```javascript
const similarity = await service.compareFaces(features1, features2);
// Expected: Returns similarity score between 0 and 1
```

### 4. Surveillance Search Tests

#### Test Case 8: Video Frame Search
```javascript
const videoFrames = [{
  id: 'frame1',
  timestamp: new Date().toISOString(),
  location: { lat: 37.7749, lng: -122.4194 },
  imageData: 'base64-frame-data'
}];

const matches = await service.searchInSurveillanceFeeds(caseId, videoFrames);
// Expected: Returns array of potential matches
```

### 5. Case Management Tests

#### Test Case 9: Case Retrieval
```javascript
const retrievedCase = service.getCase(caseId);
// Expected: Returns case data matching the ID
```

#### Test Case 10: Active Cases List
```javascript
const activeCases = service.getActiveCases();
// Expected: Returns array of all active cases
```

#### Test Case 11: Person Found Scenario
```javascript
const mockMatch = {
  similarity: 0.95,
  timestamp: new Date().toISOString(),
  location: { lat: 37.7749, lng: -122.4194 },
  confidence: 0.95
};

await service.handlePersonFound(caseId, mockMatch);
// Expected: Case status changes to 'found'
```

## Interactive Demo Testing Steps

### Step 1: Report Missing Person
1. Fill out the missing person form:
   - **Name**: Test Person
   - **Age**: 25
   - **Description**: Test case for functionality
   - **Last Seen Location**: Test Location
   - **Contact Info**: <EMAIL>
   - **Upload Photo**: Optional reference image

2. Click "Report Missing Person"
3. **Expected Result**: New case appears in Active Cases list

### Step 2: Run Automated Tests
1. Click "Run Automated Tests"
2. **Expected Results**:
   - ✅ Test 1 PASSED: Case creation
   - ✅ Test 2 PASSED: Case retrieval
   - ✅ Test 3 PASSED: Priority calculation (child)
   - ✅ Test 4 PASSED: Critical priority calculation

### Step 3: Simulate Person Found
1. Find an active case in the cases list
2. Click "Simulate Found" button
3. **Expected Result**: Case status changes to "found"

### Step 4: Monitor Test Results
- Check the Test Results section for real-time feedback
- Green results indicate successful operations
- Red results indicate errors or failures

## Expected Behaviors

### Success Indicators
- ✅ Service initializes without errors
- ✅ Cases are created with unique IDs
- ✅ Priority calculation works correctly
- ✅ Face features can be extracted from images
- ✅ Cases can be retrieved and updated
- ✅ Person found scenario updates case status

### Error Scenarios
- ❌ Missing API key: Service fails to initialize
- ❌ Invalid image data: Face extraction returns null
- ❌ Non-existent case ID: Returns undefined
- ❌ Network issues: API calls fail gracefully

## Performance Expectations

### Response Times
- Service initialization: < 2 seconds
- Case creation: < 1 second
- Face feature extraction: 2-5 seconds (depends on image size)
- Face comparison: < 500ms
- Case retrieval: < 100ms

### Accuracy Metrics
- Face detection confidence: > 0.8 for clear images
- Face matching threshold: 0.75 (configurable)
- False positive rate: < 5%

## Troubleshooting

### Common Issues

1. **Service Won't Initialize**
   - Check Google Cloud API key
   - Verify Vision API is enabled
   - Ensure billing is set up

2. **Face Detection Fails**
   - Check image format (JPEG, PNG supported)
   - Ensure image contains visible faces
   - Verify image size (< 20MB)

3. **No Test Results**
   - Check browser console for errors
   - Verify network connectivity
   - Check API quotas and limits

### Debug Mode
```javascript
// Enable debug logging
localStorage.setItem('debug-lost-found', 'true');
```

## API Quotas and Limits

- Vision API: 1000 requests/month (free tier)
- Image size limit: 20MB
- Concurrent requests: 10/second
- Face detection: Up to 50 faces per image

## Security Considerations

- API keys should be secured
- Personal data should be encrypted
- Images should be processed securely
- Access logs should be maintained

## Next Steps

After successful testing:
1. Configure production API keys
2. Set up monitoring and alerting
3. Implement data retention policies
4. Train staff on the system
5. Conduct user acceptance testing
