import React, { useState, useEffect, useRef } from 'react';
import LostAndFoundService from '../services/lostAndFoundService';
import './LostAndFoundDemo.css';

const LostAndFoundDemo = () => {
  const [service, setService] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [activeCases, setActiveCases] = useState([]);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const fileInputRef = useRef(null);

  // Form state for reporting missing person
  const [formData, setFormData] = useState({
    name: '',
    age: '',
    description: '',
    lastSeenLocation: '',
    contactInfo: '',
    medicalConditions: false,
    specialNeeds: false
  });

  useEffect(() => {
    initializeService();
    return () => {
      if (service) {
        service.destroy();
      }
    };
  }, []);

  const initializeService = async () => {
    try {
      const lostFoundService = new LostAndFoundService();
      
      const callbacks = {
        onPersonFound: (data) => {
          addTestResult('success', `Person found for case ${data.caseId}`, data);
          updateActiveCases();
        },
        onNewCase: (caseData) => {
          addTestResult('info', `New case created: ${caseData.id}`, caseData);
          updateActiveCases();
        },
        onCaseUpdate: (caseData) => {
          addTestResult('info', `Case updated: ${caseData.id}`, caseData);
          updateActiveCases();
        },
        onError: (message, error) => {
          addTestResult('error', `Error: ${message}`, error);
        }
      };

      const initialized = await lostFoundService.initialize(callbacks);
      
      if (initialized) {
        setService(lostFoundService);
        setIsInitialized(true);
        addTestResult('success', 'Lost & Found Service initialized successfully');
      } else {
        addTestResult('error', 'Failed to initialize Lost & Found Service');
      }
    } catch (error) {
      addTestResult('error', 'Service initialization error', error);
    }
  };

  const addTestResult = (type, message, data = null) => {
    const result = {
      id: Date.now(),
      type,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev.slice(0, 19)]); // Keep last 20 results
  };

  const updateActiveCases = () => {
    if (service) {
      setActiveCases(service.getActiveCases());
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const base64Data = event.target.result.split(',')[1];
        setFormData(prev => ({
          ...prev,
          referencePhoto: base64Data
        }));
        addTestResult('info', 'Reference photo loaded');
      };
      reader.readAsDataURL(file);
    }
  };

  const reportMissingPerson = async () => {
    if (!service || !isInitialized) {
      addTestResult('error', 'Service not initialized');
      return;
    }

    try {
      const personData = {
        ...formData,
        age: parseInt(formData.age) || 0,
        lastSeen: new Date().toISOString()
      };

      addTestResult('info', 'Reporting missing person...');
      const caseData = await service.reportMissingPerson(personData);
      
      if (caseData) {
        addTestResult('success', `Missing person case created: ${caseData.id}`);
        updateActiveCases();
        
        // Reset form
        setFormData({
          name: '',
          age: '',
          description: '',
          lastSeenLocation: '',
          contactInfo: '',
          medicalConditions: false,
          specialNeeds: false
        });
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    } catch (error) {
      addTestResult('error', 'Failed to report missing person', error);
    }
  };

  const runAutomatedTests = async () => {
    if (!service || !isInitialized) {
      addTestResult('error', 'Service not initialized');
      return;
    }

    setIsRunningTests(true);
    addTestResult('info', 'Starting automated tests...');

    try {
      // Test 1: Create test case
      const testPersonData = {
        name: 'Test Person',
        age: 25,
        description: 'Test case for automated testing',
        lastSeenLocation: 'Test Location',
        contactInfo: '<EMAIL>',
        medicalConditions: false,
        specialNeeds: false
      };

      const testCase = await service.reportMissingPerson(testPersonData);
      if (testCase) {
        addTestResult('success', 'Test 1 PASSED: Case creation');
      } else {
        addTestResult('error', 'Test 1 FAILED: Case creation');
      }

      // Test 2: Check case retrieval
      const retrievedCase = service.getCase(testCase?.id);
      if (retrievedCase && retrievedCase.id === testCase?.id) {
        addTestResult('success', 'Test 2 PASSED: Case retrieval');
      } else {
        addTestResult('error', 'Test 2 FAILED: Case retrieval');
      }

      // Test 3: Priority calculation
      const highPriorityData = { ...testPersonData, age: 8 };
      const highPriorityCase = await service.reportMissingPerson(highPriorityData);
      if (highPriorityCase && highPriorityCase.priority === 'high') {
        addTestResult('success', 'Test 3 PASSED: Priority calculation (child)');
      } else {
        addTestResult('error', 'Test 3 FAILED: Priority calculation');
      }

      // Test 4: Critical priority
      const criticalPriorityData = { ...testPersonData, medicalConditions: true };
      const criticalCase = await service.reportMissingPerson(criticalPriorityData);
      if (criticalCase && criticalCase.priority === 'critical') {
        addTestResult('success', 'Test 4 PASSED: Critical priority calculation');
      } else {
        addTestResult('error', 'Test 4 FAILED: Critical priority calculation');
      }

      addTestResult('success', 'Automated tests completed');
      updateActiveCases();

    } catch (error) {
      addTestResult('error', 'Automated tests failed', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const simulatePersonFound = async (caseId) => {
    if (!service) return;

    const mockMatch = {
      similarity: 0.95,
      timestamp: new Date().toISOString(),
      location: { lat: 37.7749, lng: -122.4194 },
      confidence: 0.95,
      frameId: 'mock_frame_001'
    };

    await service.handlePersonFound(caseId, mockMatch);
    addTestResult('success', `Simulated person found for case: ${caseId}`);
    updateActiveCases();
  };

  return (
    <div className="lost-found-demo">
      <div className="demo-header">
        <h2>🔍 Lost & Found Service Testing</h2>
        <div className="service-status">
          Status: <span className={isInitialized ? 'status-active' : 'status-inactive'}>
            {isInitialized ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      <div className="demo-content">
        {/* Report Missing Person Form */}
        <div className="demo-section">
          <h3>📝 Report Missing Person</h3>
          <div className="form-grid">
            <input
              type="text"
              name="name"
              placeholder="Full Name"
              value={formData.name}
              onChange={handleInputChange}
            />
            <input
              type="number"
              name="age"
              placeholder="Age"
              value={formData.age}
              onChange={handleInputChange}
            />
            <input
              type="text"
              name="description"
              placeholder="Physical Description"
              value={formData.description}
              onChange={handleInputChange}
            />
            <input
              type="text"
              name="lastSeenLocation"
              placeholder="Last Seen Location"
              value={formData.lastSeenLocation}
              onChange={handleInputChange}
            />
            <input
              type="text"
              name="contactInfo"
              placeholder="Contact Information"
              value={formData.contactInfo}
              onChange={handleInputChange}
            />
            <div className="checkbox-group">
              <label>
                <input
                  type="checkbox"
                  name="medicalConditions"
                  checked={formData.medicalConditions}
                  onChange={handleInputChange}
                />
                Medical Conditions
              </label>
              <label>
                <input
                  type="checkbox"
                  name="specialNeeds"
                  checked={formData.specialNeeds}
                  onChange={handleInputChange}
                />
                Special Needs
              </label>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              accept="image/*"
              onChange={handleFileSelect}
              placeholder="Reference Photo"
            />
          </div>
          <button 
            onClick={reportMissingPerson}
            disabled={!isInitialized || !formData.name}
            className="btn-primary"
          >
            Report Missing Person
          </button>
        </div>

        {/* Test Controls */}
        <div className="demo-section">
          <h3>🧪 Testing Controls</h3>
          <div className="test-controls">
            <button 
              onClick={runAutomatedTests}
              disabled={!isInitialized || isRunningTests}
              className="btn-secondary"
            >
              {isRunningTests ? 'Running Tests...' : 'Run Automated Tests'}
            </button>
            <button 
              onClick={updateActiveCases}
              disabled={!isInitialized}
              className="btn-secondary"
            >
              Refresh Cases
            </button>
          </div>
        </div>

        {/* Active Cases */}
        <div className="demo-section">
          <h3>📋 Active Cases ({activeCases.length})</h3>
          <div className="cases-list">
            {activeCases.map(caseData => (
              <div key={caseData.id} className={`case-card priority-${caseData.priority}`}>
                <div className="case-header">
                  <span className="case-name">{caseData.name}</span>
                  <span className={`case-status status-${caseData.status}`}>
                    {caseData.status}
                  </span>
                </div>
                <div className="case-details">
                  <p><strong>Age:</strong> {caseData.age}</p>
                  <p><strong>Priority:</strong> {caseData.priority}</p>
                  <p><strong>Reported:</strong> {new Date(caseData.reportedAt).toLocaleString()}</p>
                  <p><strong>Location:</strong> {caseData.lastSeenLocation}</p>
                  {caseData.matches && caseData.matches.length > 0 && (
                    <p><strong>Matches:</strong> {caseData.matches.length}</p>
                  )}
                </div>
                {caseData.status === 'active' && (
                  <button 
                    onClick={() => simulatePersonFound(caseData.id)}
                    className="btn-success btn-small"
                  >
                    Simulate Found
                  </button>
                )}
              </div>
            ))}
            {activeCases.length === 0 && (
              <div className="no-cases">No active cases</div>
            )}
          </div>
        </div>

        {/* Test Results */}
        <div className="demo-section">
          <h3>📊 Test Results</h3>
          <div className="results-list">
            {testResults.map(result => (
              <div key={result.id} className={`result-item result-${result.type}`}>
                <span className="result-time">{result.timestamp}</span>
                <span className="result-message">{result.message}</span>
                {result.data && (
                  <details className="result-details">
                    <summary>View Details</summary>
                    <pre>{JSON.stringify(result.data, null, 2)}</pre>
                  </details>
                )}
              </div>
            ))}
            {testResults.length === 0 && (
              <div className="no-results">No test results yet</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LostAndFoundDemo;
