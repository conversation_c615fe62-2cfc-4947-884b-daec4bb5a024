.lost-found-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(15, 15, 35, 0.95);
  border-radius: 16px;
  color: white;
  font-family: 'Inter', sans-serif;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(120, 219, 255, 0.2);
}

.demo-header h2 {
  margin: 0;
  font-size: 1.8rem;
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.service-status {
  font-weight: 600;
}

.status-active {
  color: #28a745;
}

.status-inactive {
  color: #dc3545;
}

.demo-content {
  display: grid;
  gap: 2rem;
}

.demo-section {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(120, 219, 255, 0.2);
}

.demo-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: #78dbff;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-grid input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(120, 219, 255, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  color: white;
  font-size: 0.9rem;
}

.form-grid input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-grid input:focus {
  outline: none;
  border-color: #78dbff;
  box-shadow: 0 0 0 2px rgba(120, 219, 255, 0.2);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Button Styles */
.btn-primary, .btn-secondary, .btn-success {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  color: white;
}

.btn-secondary {
  background: rgba(120, 219, 255, 0.2);
  color: #78dbff;
  border: 1px solid #78dbff;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.btn-primary:hover, .btn-secondary:hover, .btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-primary:disabled, .btn-secondary:disabled, .btn-success:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Test Controls */
.test-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Cases List */
.cases-list {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.case-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid;
}

.case-card.priority-low {
  border-left-color: #28a745;
}

.case-card.priority-medium {
  border-left-color: #ffc107;
}

.case-card.priority-high {
  border-left-color: #fd7e14;
}

.case-card.priority-critical {
  border-left-color: #dc3545;
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.case-name {
  font-weight: 600;
  font-size: 1rem;
}

.case-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-found {
  background: rgba(0, 123, 255, 0.2);
  color: #007bff;
}

.status-expired {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.case-details p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.no-cases, .no-results {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 2rem;
}

/* Results List */
.results-list {
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.result-item {
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid;
  background: rgba(255, 255, 255, 0.05);
}

.result-item.result-success {
  border-left-color: #28a745;
}

.result-item.result-error {
  border-left-color: #dc3545;
}

.result-item.result-info {
  border-left-color: #17a2b8;
}

.result-time {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-right: 1rem;
}

.result-message {
  font-size: 0.9rem;
}

.result-details {
  margin-top: 0.5rem;
}

.result-details summary {
  cursor: pointer;
  font-size: 0.8rem;
  color: #78dbff;
}

.result-details pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
  margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .lost-found-demo {
    padding: 1rem;
  }
  
  .demo-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .test-controls {
    flex-direction: column;
  }
  
  .cases-list {
    grid-template-columns: 1fr;
  }
}
