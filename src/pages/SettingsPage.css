.settings-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  overflow: hidden;
}

.page-header {
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  padding: 2rem;
  border-bottom: 1px solid rgba(120, 219, 255, 0.2);
  text-align: center;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  margin: 0;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.settings-section {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(120, 219, 255, 0.2);
}

.settings-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #78dbff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-item {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  font-size: 0.95rem;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #78dbff;
}

.setting-item input[type="range"] {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #78dbff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(120, 219, 255, 0.5);
}

.setting-item input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #78dbff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(120, 219, 255, 0.5);
}

.setting-item input[type="number"],
.setting-item select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(120, 219, 255, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  color: #ffffff;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.setting-item input[type="number"]:focus,
.setting-item select:focus {
  border-color: #78dbff;
  box-shadow: 0 0 10px rgba(120, 219, 255, 0.3);
}

.setting-item select option {
  background: #1a1a2e;
  color: #ffffff;
}

.setting-item span {
  font-size: 0.9rem;
  color: #78dbff;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
}

.settings-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(120, 219, 255, 0.2);
}

.save-btn,
.reset-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(120, 219, 255, 0.3);
  color: white;
  padding: 1rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 1rem;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.save-btn::before,
.reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.save-btn:hover::before,
.reset-btn:hover::before {
  left: 100%;
}

.save-btn:hover {
  background: rgba(120, 219, 255, 0.15);
  border-color: rgba(120, 219, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(120, 219, 255, 0.3);
}

.save-btn {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border-color: #28a745;
}

.save-btn:hover {
  background: linear-gradient(135deg, #34ce57 0%, #28a745 100%);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.reset-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border-color: #dc3545;
}

.reset-btn:hover {
  background: linear-gradient(135deg, #e4606d 0%, #dc3545 100%);
  box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

/* Lost and Found Demo Styles */
.full-width {
  grid-column: 1 / -1;
}

.demo-toggle-btn {
  background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.demo-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.demo-toggle-btn.active {
  background: linear-gradient(135deg, #ff77c6 0%, #78dbff 100%);
}

.demo-container {
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(120, 219, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .settings-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .settings-content {
    padding: 1rem;
  }
  
  .settings-section {
    padding: 1rem;
  }
  
  .settings-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .save-btn,
  .reset-btn {
    width: 100%;
    max-width: 300px;
  }
}
