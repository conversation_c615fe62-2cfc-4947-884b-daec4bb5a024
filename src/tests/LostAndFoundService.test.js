import LostAndFoundService from '../services/lostAndFoundService';

// Mock Firebase
jest.mock('../firebase', () => ({
  db: {},
}));

// Mock Firestore functions
jest.mock('firebase/firestore', () => ({
  collection: jest.fn(() => 'mock-collection'),
  addDoc: jest.fn(() => Promise.resolve({ id: 'mock-doc-id' })),
  onSnapshot: jest.fn(() => () => {}), // Return unsubscribe function
  query: jest.fn(() => 'mock-query'),
  where: jest.fn(() => 'mock-where'),
  orderBy: jest.fn(() => 'mock-orderBy'),
}));

// Mock axios
jest.mock('axios', () => ({
  post: jest.fn(() => Promise.resolve({
    data: {
      responses: [{
        faceAnnotations: [{
          landmarks: [
            { type: 'LEFT_EYE', position: { x: 100, y: 100, z: 0 } },
            { type: 'RIGHT_EYE', position: { x: 150, y: 100, z: 0 } },
            { type: 'NOSE_TIP', position: { x: 125, y: 125, z: 0 } }
          ],
          boundingPoly: { vertices: [{ x: 80, y: 80 }, { x: 170, y: 80 }, { x: 170, y: 170 }, { x: 80, y: 170 }] },
          fdBoundingPoly: { vertices: [{ x: 85, y: 85 }, { x: 165, y: 85 }, { x: 165, y: 165 }, { x: 85, y: 165 }] },
          rollAngle: 0.5,
          panAngle: 1.2,
          tiltAngle: -0.8,
          detectionConfidence: 0.95
        }]
      }]
    }
  }))
}));

describe('LostAndFoundService', () => {
  let service;
  let mockCallbacks;

  beforeEach(() => {
    service = new LostAndFoundService();
    mockCallbacks = {
      onPersonFound: jest.fn(),
      onNewCase: jest.fn(),
      onCaseUpdate: jest.fn(),
      onError: jest.fn()
    };
    
    // Mock environment variables
    process.env.REACT_APP_GOOGLE_CLOUD_API_KEY = 'mock-api-key';
    process.env.REACT_APP_GOOGLE_CLOUD_PROJECT_ID = 'mock-project-id';
  });

  afterEach(() => {
    if (service) {
      service.destroy();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize successfully with valid API key', async () => {
      const result = await service.initialize(mockCallbacks);
      
      expect(result).toBe(true);
      expect(service.isInitialized).toBe(true);
    });

    test('should fail initialization without API key', async () => {
      delete process.env.REACT_APP_GOOGLE_CLOUD_API_KEY;
      service = new LostAndFoundService();
      
      const result = await service.initialize(mockCallbacks);
      
      expect(result).toBe(false);
      expect(service.isInitialized).toBe(false);
      expect(mockCallbacks.onError).toHaveBeenCalled();
    });
  });

  describe('Missing Person Reporting', () => {
    beforeEach(async () => {
      await service.initialize(mockCallbacks);
    });

    test('should create a missing person case successfully', async () => {
      const personData = {
        name: 'John Doe',
        age: 30,
        description: 'Tall, brown hair',
        lastSeenLocation: 'Central Park',
        contactInfo: '<EMAIL>'
      };

      const caseData = await service.reportMissingPerson(personData);

      expect(caseData).toBeTruthy();
      expect(caseData.name).toBe('John Doe');
      expect(caseData.status).toBe('active');
      expect(caseData.priority).toBe('medium');
      expect(caseData.id).toMatch(/^LF_/);
      expect(mockCallbacks.onNewCase).toHaveBeenCalledWith(caseData);
    });

    test('should calculate high priority for children', async () => {
      const childData = {
        name: 'Child Doe',
        age: 8,
        description: 'Small child',
        lastSeenLocation: 'Playground'
      };

      const caseData = await service.reportMissingPerson(childData);

      expect(caseData.priority).toBe('high');
    });

    test('should calculate high priority for elderly', async () => {
      const elderlyData = {
        name: 'Elder Doe',
        age: 75,
        description: 'Elderly person',
        lastSeenLocation: 'Park'
      };

      const caseData = await service.reportMissingPerson(elderlyData);

      expect(caseData.priority).toBe('high');
    });

    test('should calculate critical priority for medical conditions', async () => {
      const medicalData = {
        name: 'Medical Doe',
        age: 40,
        description: 'Person with medical needs',
        lastSeenLocation: 'Hospital',
        medicalConditions: true
      };

      const caseData = await service.reportMissingPerson(medicalData);

      expect(caseData.priority).toBe('critical');
    });

    test('should calculate critical priority for special needs', async () => {
      const specialNeedsData = {
        name: 'Special Doe',
        age: 25,
        description: 'Person with special needs',
        lastSeenLocation: 'Care Center',
        specialNeeds: true
      };

      const caseData = await service.reportMissingPerson(specialNeedsData);

      expect(caseData.priority).toBe('critical');
    });
  });

  describe('Face Feature Extraction', () => {
    beforeEach(async () => {
      await service.initialize(mockCallbacks);
    });

    test('should extract face features from image', async () => {
      const mockImageData = 'base64-encoded-image-data';
      
      const features = await service.extractFaceFeatures(mockImageData);

      expect(features).toBeTruthy();
      expect(features.landmarks).toBeDefined();
      expect(features.boundingPoly).toBeDefined();
      expect(features.detectionConfidence).toBe(0.95);
    });

    test('should return null for invalid image data', async () => {
      // Mock axios to return empty response
      const axios = require('axios');
      axios.post.mockResolvedValueOnce({
        data: { responses: [{ faceAnnotations: [] }] }
      });

      const features = await service.extractFaceFeatures('invalid-data');

      expect(features).toBeNull();
    });
  });

  describe('Face Comparison', () => {
    beforeEach(async () => {
      await service.initialize(mockCallbacks);
    });

    test('should compare faces and return similarity score', async () => {
      const features1 = {
        landmarks: [
          { type: 'LEFT_EYE', position: { x: 100, y: 100, z: 0 } },
          { type: 'RIGHT_EYE', position: { x: 150, y: 100, z: 0 } }
        ]
      };

      const features2 = {
        landmarks: [
          { type: 'LEFT_EYE', position: { x: 102, y: 102, z: 0 } },
          { type: 'RIGHT_EYE', position: { x: 148, y: 98, z: 0 } }
        ]
      };

      const similarity = await service.compareFaces(features1, features2);

      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThanOrEqual(1);
    });

    test('should return 0 for faces without landmarks', async () => {
      const features1 = { landmarks: null };
      const features2 = { landmarks: [] };

      const similarity = await service.compareFaces(features1, features2);

      expect(similarity).toBe(0);
    });
  });

  describe('Case Management', () => {
    beforeEach(async () => {
      await service.initialize(mockCallbacks);
    });

    test('should retrieve case by ID', async () => {
      const personData = {
        name: 'Test Person',
        age: 30,
        description: 'Test case'
      };

      const caseData = await service.reportMissingPerson(personData);
      const retrievedCase = service.getCase(caseData.id);

      expect(retrievedCase).toEqual(caseData);
    });

    test('should return all active cases', async () => {
      const personData1 = { name: 'Person 1', age: 25 };
      const personData2 = { name: 'Person 2', age: 35 };

      await service.reportMissingPerson(personData1);
      await service.reportMissingPerson(personData2);

      const activeCases = service.getActiveCases();

      expect(activeCases).toHaveLength(2);
      expect(activeCases[0].status).toBe('active');
      expect(activeCases[1].status).toBe('active');
    });

    test('should handle person found scenario', async () => {
      const personData = {
        name: 'Found Person',
        age: 30
      };

      const caseData = await service.reportMissingPerson(personData);
      const mockMatch = {
        similarity: 0.95,
        timestamp: new Date().toISOString(),
        location: { lat: 37.7749, lng: -122.4194 },
        confidence: 0.95
      };

      await service.handlePersonFound(caseData.id, mockMatch);

      const updatedCase = service.getCase(caseData.id);
      expect(updatedCase.status).toBe('found');
      expect(updatedCase.foundMatch).toEqual(mockMatch);
      expect(mockCallbacks.onPersonFound).toHaveBeenCalled();
    });
  });

  describe('Surveillance Search', () => {
    beforeEach(async () => {
      await service.initialize(mockCallbacks);
    });

    test('should search for person in video frames', async () => {
      const personData = {
        name: 'Search Person',
        age: 30,
        referencePhoto: 'base64-image-data'
      };

      const caseData = await service.reportMissingPerson(personData);
      
      const mockVideoFrames = [{
        id: 'frame1',
        timestamp: new Date().toISOString(),
        location: { lat: 37.7749, lng: -122.4194 },
        imageData: 'base64-frame-data'
      }];

      const matches = await service.searchInSurveillanceFeeds(caseData.id, mockVideoFrames);

      expect(Array.isArray(matches)).toBe(true);
    });
  });

  describe('Service Cleanup', () => {
    test('should cleanup resources on destroy', async () => {
      await service.initialize(mockCallbacks);

      // Manually add a case to test cleanup
      service.activeCases.set('test-case', { id: 'test-case', name: 'Test' });

      expect(service.getActiveCases()).toHaveLength(1);

      service.destroy();

      expect(service.getActiveCases()).toHaveLength(0);
      expect(service.isInitialized).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    test('should generate unique case IDs', () => {
      const id1 = service.generateCaseId();
      const id2 = service.generateCaseId();

      expect(id1).toMatch(/^LF_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^LF_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    test('should calculate distance between points', () => {
      const point1 = { x: 0, y: 0, z: 0 };
      const point2 = { x: 3, y: 4, z: 0 };

      const distance = service.calculateDistance(point1, point2);

      expect(distance).toBe(5); // 3-4-5 triangle
    });
  });
});
